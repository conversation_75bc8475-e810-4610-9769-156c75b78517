import sqlite3
import pandas as pd

conn = sqlite3.connect('database.sqlite')

print('=== ESTRUTURA ATUAL DO DATABASE.SQLITE ===')
cursor = conn.cursor()
cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
tables = cursor.fetchall()

for table in tables:
    table_name = table[0]
    print(f'\n📋 {table_name}:')
    
    cursor.execute(f'PRAGMA table_info({table_name})')
    columns = cursor.fetchall()
    for col in columns:
        print(f'  - {col[1]} ({col[2]})')
    
    cursor.execute(f'SELECT COUNT(*) FROM {table_name}')
    count = cursor.fetchone()[0]
    print(f'  Total: {count:,} registros')
    
    if 'resultados_concursos' in table_name and count > 0:
        print('  Exemplo:')
        df = pd.read_sql_query(f'SELECT * FROM {table_name} LIMIT 2', conn)
        print(df[['id_concurso', 'resultado', 'pares_impares', 'soma_numeros_resultaado']].to_string())

conn.close()
