import sqlite3
import pandas as pd

def verify_consolidation():
    """
    Verifica o resultado da consolidação e limpa tabelas restantes se necessário
    """
    
    print("🔍 Verificando resultado da consolidação...")
    conn = sqlite3.connect('database.sqlite')
    cursor = conn.cursor()
    
    try:
        # Verificar estrutura atual
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f"\n📋 ESTRUTURA ATUAL DO DATABASE:")
        for table in tables:
            table_name = table[0]
            
            # Pular tabelas do sistema SQLite
            if table_name.startswith('sqlite_'):
                continue
                
            cursor.execute(f'SELECT COUNT(*) FROM {table_name}')
            count = cursor.fetchone()[0]
            
            print(f"   📊 {table_name}: {count:,} registros")
            
            # Mostrar estrutura das colunas
            cursor.execute(f'PRAGMA table_info({table_name})')
            columns = cursor.fetchall()
            print(f"      Colunas: {[col[1] for col in columns]}")
        
        # Verificar se ainda existem tabelas para deletar (exceto as do sistema)
        tabelas_manter = ['todos_os_jogos', 'resultados_concursos']
        tabelas_deletar = []
        
        for table in tables:
            table_name = table[0]
            if not table_name.startswith('sqlite_') and table_name not in tabelas_manter:
                tabelas_deletar.append(table_name)
        
        if tabelas_deletar:
            print(f"\n🗑️  Removendo tabelas restantes: {tabelas_deletar}")
            for table in tabelas_deletar:
                cursor.execute(f"DROP TABLE IF EXISTS {table}")
                print(f"   ✅ Tabela {table} removida")
            conn.commit()
        
        # Verificar dados da tabela consolidada
        if 'todos_os_jogos' in [t[0] for t in tables]:
            print(f"\n📊 AMOSTRA DA TABELA 'todos_os_jogos':")
            df_sample = pd.read_sql_query("SELECT * FROM todos_os_jogos LIMIT 5", conn)
            print(df_sample.to_string())
            
            # Estatísticas
            cursor.execute("SELECT COUNT(*) FROM todos_os_jogos")
            total = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(DISTINCT Resultado1) FROM todos_os_jogos")
            unique_results = cursor.fetchone()[0]
            
            cursor.execute("SELECT MIN(Soma), MAX(Soma), AVG(Soma) FROM todos_os_jogos")
            soma_stats = cursor.fetchone()
            
            print(f"\n📈 ESTATÍSTICAS:")
            print(f"   Total de registros: {total:,}")
            print(f"   Resultados únicos: {unique_results:,}")
            print(f"   Soma - Min: {soma_stats[0]}, Max: {soma_stats[1]}, Média: {soma_stats[2]:.2f}")
        
        # Verificar tabela resultados_concursos
        if 'resultados_concursos' in [t[0] for t in tables]:
            cursor.execute("SELECT COUNT(*) FROM resultados_concursos")
            count_concursos = cursor.fetchone()[0]
            print(f"\n📊 Tabela 'resultados_concursos': {count_concursos:,} registros mantidos")
        
        print(f"\n✅ CONSOLIDAÇÃO FINALIZADA COM SUCESSO!")
        print(f"📋 Database agora contém apenas as tabelas necessárias:")
        print(f"   - todos_os_jogos: Dados consolidados das 4 planilhas")
        print(f"   - resultados_concursos: Dados dos concursos (mantida)")
        
    except Exception as e:
        print(f"❌ Erro: {str(e)}")
    
    finally:
        conn.close()

if __name__ == "__main__":
    verify_consolidation()
